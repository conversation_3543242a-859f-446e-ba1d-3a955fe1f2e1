#!/bin/bash

# This script downloads and sets up tree-sitter WASM files for typescript/tsx
# using Emscripten to compile the C code to WebAssembly

set -e # Exit immediately if a command exits with a non-zero status

echo "Setting up Tree Sitter WASM files..."

# Make sure we're in the project root directory
cd "$(dirname "$0")/.." || exit 1
PROJECT_ROOT=$(pwd)

# Create directory for wasm files if it doesn't exist
mkdir -p public/tree-sitter-wasm

# Copy web-tree-sitter wasm file to public directory
echo "Copying web-tree-sitter wasm file..."
if [ -f "node_modules/web-tree-sitter/tree-sitter.wasm" ]; then
    cp node_modules/web-tree-sitter/tree-sitter.wasm public/tree-sitter-wasm/
else
    echo "ERROR: tree-sitter.wasm not found in node_modules/web-tree-sitter/"
    echo "Make sure web-tree-sitter is installed: npm install web-tree-sitter"
    exit 1
fi

# Check if tree-sitter-cli is available via npx, install as dev dependency if not
if ! npx tree-sitter --version &> /dev/null; then
    echo "tree-sitter-cli not found or not working via npx, installing as dev dependency..."
    npm install --save-dev tree-sitter-cli
    if ! npx tree-sitter --version &> /dev/null; then
        echo "Failed to install or run tree-sitter-cli. Please check your npm setup."
        exit 1
    fi
fi

# Ensure tree-sitter-typescript is installed as a dev dependency
if [ ! -d "node_modules/tree-sitter-typescript/typescript" ] || [ ! -d "node_modules/tree-sitter-typescript/tsx" ]; then
    echo "tree-sitter-typescript package not found or incomplete, installing as dev dependency..."
    npm install --save-dev tree-sitter-typescript
    if [ ! -d "node_modules/tree-sitter-typescript/typescript" ] || [ ! -d "node_modules/tree-sitter-typescript/tsx" ]; then
        echo "Failed to install tree-sitter-typescript or grammars not found. Please check the package."
        exit 1
    fi
fi

# Check if emcc is already available
if ! command -v emcc &> /dev/null; then
    echo "emcc not found in PATH, setting up Emscripten SDK..."
    
    # Set up emsdk in the project directory
    EMSDK_DIR="$PROJECT_ROOT/tools/emsdk"
    
    # Create tools directory if it doesn't exist
    mkdir -p "$PROJECT_ROOT/tools"
    
    # Check if emsdk already exists
    if [ ! -d "$EMSDK_DIR" ]; then
        echo "Cloning emsdk repository..."
        git clone https://github.com/emscripten-core/emsdk.git "$EMSDK_DIR"
    else
        echo "emsdk directory already exists, updating..."
        cd "$EMSDK_DIR" && git pull && cd "$PROJECT_ROOT"
    fi
    
    # Install and activate the latest SDK
    cd "$EMSDK_DIR"
    ./emsdk install latest
    ./emsdk activate latest
    
    # Source the environment variables
    source ./emsdk_env.sh
    
    # Return to project root
    cd "$PROJECT_ROOT"
    
    # Verify emcc is now available
    if ! command -v emcc &> /dev/null; then
        echo "ERROR: Failed to set up emcc even after installing emsdk."
        exit 1
    fi
    
    echo "Emscripten SDK set up successfully."
fi

# Print emcc version for debugging
emcc --version

# Helper function to download pre-built WASM files from GitHub releases as a fallback
download_prebuilt_wasm() {
    local grammar_name=$1
    local output_name=$2
    local github_url="https://github.com/tree-sitter/tree-sitter-${grammar_name}/releases/latest/download/${grammar_name}.wasm"
    
    echo "Attempting to download pre-built ${grammar_name}.wasm from GitHub..."
    if curl -L -o "${output_name}" "${github_url}"; then
        echo "Successfully downloaded ${output_name}"
        return 0
    else
        echo "Failed to download ${output_name} from GitHub"
        return 1
    fi
}

# Function to check if we need to fallback to downloading or using a locally included WASM file
build_or_download_wasm() {
    local grammar_dir=$1    # Directory with grammar (e.g., node_modules/tree-sitter-typescript/typescript)
    local grammar_name=$2   # Name for the grammar (e.g., typescript)
    local output_name=$3    # Final name for the WASM file (e.g., tree-sitter-typescript.wasm)
    
    # First, try to build using tree-sitter-cli with verbose output
    echo "Building ${grammar_name} WASM (${output_name})..."
    echo "Running: npx tree-sitter build --wasm ${grammar_dir}"
    
    # Try to build with increased verbosity
    if EMSDK_VERBOSE=1 npx tree-sitter build --wasm "${grammar_dir}"; then
        # Check if the expected output file exists
        if [ -f "${grammar_name}.wasm" ]; then
            echo "Successfully built ${grammar_name}.wasm"
            echo "Renaming ${grammar_name}.wasm to ${output_name}"
            mv "${grammar_name}.wasm" "${output_name}"
            echo "Copying ${output_name} to public/tree-sitter-wasm/"
            cp "${output_name}" "public/tree-sitter-wasm/"
            rm "${output_name}" # Clean up the local copy
            return 0
        else
            echo "WARNING: ${grammar_name}.wasm not found after build despite successful command."
            # List all files in current directory for debugging
            echo "Current directory contents:"
            ls -la
        fi
    else
        echo "WARNING: Failed to build ${grammar_name}.wasm using tree-sitter-cli."
    fi
    
    # Try fallback: download pre-built WASM from GitHub
    if download_prebuilt_wasm "${grammar_name}" "${output_name}"; then
        echo "Copying ${output_name} to public/tree-sitter-wasm/"
        cp "${output_name}" "public/tree-sitter-wasm/"
        rm "${output_name}" # Clean up the local copy
        return 0
    else
        echo "ERROR: All methods failed to obtain ${output_name}"
        return 1
    fi
}

# Build/download TypeScript WASM
if ! build_or_download_wasm "node_modules/tree-sitter-typescript/typescript" "typescript" "tree-sitter-typescript.wasm"; then
    echo "ERROR: Failed to obtain tree-sitter-typescript.wasm"
    exit 1
fi

# Build/download TSX WASM
if ! build_or_download_wasm "node_modules/tree-sitter-typescript/tsx" "tsx" "tree-sitter-tsx.wasm"; then
    echo "ERROR: Failed to obtain tree-sitter-tsx.wasm"
    exit 1
fi

echo "Tree Sitter WASM setup complete. All WASM files have been placed in public/tree-sitter-wasm/"
ls -la public/tree-sitter-wasm/

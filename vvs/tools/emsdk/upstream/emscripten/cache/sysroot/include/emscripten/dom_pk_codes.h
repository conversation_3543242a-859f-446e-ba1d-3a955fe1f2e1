/*
 * Copyright 2018 The Emscripten Authors.  All rights reserved.
 * Emscripten is available under two separate licenses, the MIT license and the
 * University of Illinois/NCSA Open Source License.  Both these licenses can be
 * found in the LICENSE file.
 *
 * This file was automatically generated from script
 * tools/maint/create_dom_pk_codes.py. Edit that file to make changes here.
 * Then run:
 *
 *   tools/maint/create_dom_pk_codes.py
 *
 * in Emscripten root directory to regenerate this file.
 */

#pragma once

#define DOM_PK_CODE_TYPE int

#define DOM_PK_UNKNOWN              0x0000 /* "Unidentified"       */
#define DOM_PK_ESCAPE               0x0001 /* "Escape"             */
#define DOM_PK_0                    0x0002 /* "Digit0"             */
#define DOM_PK_1                    0x0003 /* "Digit1"             */
#define DOM_PK_2                    0x0004 /* "Digit2"             */
#define DOM_PK_3                    0x0005 /* "Digit3"             */
#define DOM_PK_4                    0x0006 /* "Digit4"             */
#define DOM_PK_5                    0x0007 /* "Digit5"             */
#define DOM_PK_6                    0x0008 /* "Digit6"             */
#define DOM_PK_7                    0x0009 /* "Digit7"             */
#define DOM_PK_8                    0x000A /* "Digit8"             */
#define DOM_PK_9                    0x000B /* "Digit9"             */
#define DOM_PK_MINUS                0x000C /* "Minus"              */
#define DOM_PK_EQUAL                0x000D /* "Equal"              */
#define DOM_PK_BACKSPACE            0x000E /* "Backspace"          */
#define DOM_PK_TAB                  0x000F /* "Tab"                */
#define DOM_PK_Q                    0x0010 /* "KeyQ"               */
#define DOM_PK_W                    0x0011 /* "KeyW"               */
#define DOM_PK_E                    0x0012 /* "KeyE"               */
#define DOM_PK_R                    0x0013 /* "KeyR"               */
#define DOM_PK_T                    0x0014 /* "KeyT"               */
#define DOM_PK_Y                    0x0015 /* "KeyY"               */
#define DOM_PK_U                    0x0016 /* "KeyU"               */
#define DOM_PK_I                    0x0017 /* "KeyI"               */
#define DOM_PK_O                    0x0018 /* "KeyO"               */
#define DOM_PK_P                    0x0019 /* "KeyP"               */
#define DOM_PK_BRACKET_LEFT         0x001A /* "BracketLeft"        */
#define DOM_PK_BRACKET_RIGHT        0x001B /* "BracketRight"       */
#define DOM_PK_ENTER                0x001C /* "Enter"              */
#define DOM_PK_CONTROL_LEFT         0x001D /* "ControlLeft"        */
#define DOM_PK_A                    0x001E /* "KeyA"               */
#define DOM_PK_S                    0x001F /* "KeyS"               */
#define DOM_PK_D                    0x0020 /* "KeyD"               */
#define DOM_PK_F                    0x0021 /* "KeyF"               */
#define DOM_PK_G                    0x0022 /* "KeyG"               */
#define DOM_PK_H                    0x0023 /* "KeyH"               */
#define DOM_PK_J                    0x0024 /* "KeyJ"               */
#define DOM_PK_K                    0x0025 /* "KeyK"               */
#define DOM_PK_L                    0x0026 /* "KeyL"               */
#define DOM_PK_SEMICOLON            0x0027 /* "Semicolon"          */
#define DOM_PK_QUOTE                0x0028 /* "Quote"              */
#define DOM_PK_BACKQUOTE            0x0029 /* "Backquote"          */
#define DOM_PK_SHIFT_LEFT           0x002A /* "ShiftLeft"          */
#define DOM_PK_BACKSLASH            0x002B /* "Backslash"          */
#define DOM_PK_Z                    0x002C /* "KeyZ"               */
#define DOM_PK_X                    0x002D /* "KeyX"               */
#define DOM_PK_C                    0x002E /* "KeyC"               */
#define DOM_PK_V                    0x002F /* "KeyV"               */
#define DOM_PK_B                    0x0030 /* "KeyB"               */
#define DOM_PK_N                    0x0031 /* "KeyN"               */
#define DOM_PK_M                    0x0032 /* "KeyM"               */
#define DOM_PK_COMMA                0x0033 /* "Comma"              */
#define DOM_PK_PERIOD               0x0034 /* "Period"             */
#define DOM_PK_SLASH                0x0035 /* "Slash"              */
#define DOM_PK_SHIFT_RIGHT          0x0036 /* "ShiftRight"         */
#define DOM_PK_NUMPAD_MULTIPLY      0x0037 /* "NumpadMultiply"     */
#define DOM_PK_ALT_LEFT             0x0038 /* "AltLeft"            */
#define DOM_PK_SPACE                0x0039 /* "Space"              */
#define DOM_PK_CAPS_LOCK            0x003A /* "CapsLock"           */
#define DOM_PK_F1                   0x003B /* "F1"                 */
#define DOM_PK_F2                   0x003C /* "F2"                 */
#define DOM_PK_F3                   0x003D /* "F3"                 */
#define DOM_PK_F4                   0x003E /* "F4"                 */
#define DOM_PK_F5                   0x003F /* "F5"                 */
#define DOM_PK_F6                   0x0040 /* "F6"                 */
#define DOM_PK_F7                   0x0041 /* "F7"                 */
#define DOM_PK_F8                   0x0042 /* "F8"                 */
#define DOM_PK_F9                   0x0043 /* "F9"                 */
#define DOM_PK_F10                  0x0044 /* "F10"                */
#define DOM_PK_PAUSE                0x0045 /* "Pause"              */
#define DOM_PK_SCROLL_LOCK          0x0046 /* "ScrollLock"         */
#define DOM_PK_NUMPAD_7             0x0047 /* "Numpad7"            */
#define DOM_PK_NUMPAD_8             0x0048 /* "Numpad8"            */
#define DOM_PK_NUMPAD_9             0x0049 /* "Numpad9"            */
#define DOM_PK_NUMPAD_SUBTRACT      0x004A /* "NumpadSubtract"     */
#define DOM_PK_NUMPAD_4             0x004B /* "Numpad4"            */
#define DOM_PK_NUMPAD_5             0x004C /* "Numpad5"            */
#define DOM_PK_NUMPAD_6             0x004D /* "Numpad6"            */
#define DOM_PK_NUMPAD_ADD           0x004E /* "NumpadAdd"          */
#define DOM_PK_NUMPAD_1             0x004F /* "Numpad1"            */
#define DOM_PK_NUMPAD_2             0x0050 /* "Numpad2"            */
#define DOM_PK_NUMPAD_3             0x0051 /* "Numpad3"            */
#define DOM_PK_NUMPAD_0             0x0052 /* "Numpad0"            */
#define DOM_PK_NUMPAD_DECIMAL       0x0053 /* "NumpadDecimal"      */
#define DOM_PK_PRINT_SCREEN         0x0054 /* "PrintScreen"        */
#define DOM_PK_INTL_BACKSLASH       0x0056 /* "IntlBackslash"      */
#define DOM_PK_F11                  0x0057 /* "F11"                */
#define DOM_PK_F12                  0x0058 /* "F12"                */
#define DOM_PK_NUMPAD_EQUAL         0x0059 /* "NumpadEqual"        */
#define DOM_PK_F13                  0x0064 /* "F13"                */
#define DOM_PK_F14                  0x0065 /* "F14"                */
#define DOM_PK_F15                  0x0066 /* "F15"                */
#define DOM_PK_F16                  0x0067 /* "F16"                */
#define DOM_PK_F17                  0x0068 /* "F17"                */
#define DOM_PK_F18                  0x0069 /* "F18"                */
#define DOM_PK_F19                  0x006A /* "F19"                */
#define DOM_PK_F20                  0x006B /* "F20"                */
#define DOM_PK_F21                  0x006C /* "F21"                */
#define DOM_PK_F22                  0x006D /* "F22"                */
#define DOM_PK_F23                  0x006E /* "F23"                */
#define DOM_PK_KANA_MODE            0x0070 /* "KanaMode"           */
#define DOM_PK_LANG_2               0x0071 /* "Lang2"              */
#define DOM_PK_LANG_1               0x0072 /* "Lang1"              */
#define DOM_PK_INTL_RO              0x0073 /* "IntlRo"             */
#define DOM_PK_F24                  0x0076 /* "F24"                */
#define DOM_PK_CONVERT              0x0079 /* "Convert"            */
#define DOM_PK_NON_CONVERT          0x007B /* "NonConvert"         */
#define DOM_PK_INTL_YEN             0x007D /* "IntlYen"            */
#define DOM_PK_NUMPAD_COMMA         0x007E /* "NumpadComma"        */
#define DOM_PK_PASTE                0xE00A /* "Paste"              */
#define DOM_PK_MEDIA_TRACK_PREVIOUS 0xE010 /* "MediaTrackPrevious" */
#define DOM_PK_CUT                  0xE017 /* "Cut"                */
#define DOM_PK_COPY                 0xE018 /* "Copy"               */
#define DOM_PK_MEDIA_TRACK_NEXT     0xE019 /* "MediaTrackNext"     */
#define DOM_PK_NUMPAD_ENTER         0xE01C /* "NumpadEnter"        */
#define DOM_PK_CONTROL_RIGHT        0xE01D /* "ControlRight"       */
#define DOM_PK_AUDIO_VOLUME_MUTE    0xE020 /* "AudioVolumeMute"    */
#define DOM_PK_AUDIO_VOLUME_MUTE    0xE020 /* "VolumeMute"         */
#define DOM_PK_LAUNCH_APP_2         0xE021 /* "LaunchApp2"         */
#define DOM_PK_MEDIA_PLAY_PAUSE     0xE022 /* "MediaPlayPause"     */
#define DOM_PK_MEDIA_STOP           0xE024 /* "MediaStop"          */
#define DOM_PK_EJECT                0xE02C /* "Eject"              */
#define DOM_PK_AUDIO_VOLUME_DOWN    0xE02E /* "AudioVolumeDown"    */
#define DOM_PK_AUDIO_VOLUME_DOWN    0xE02E /* "VolumeDown"         */
#define DOM_PK_AUDIO_VOLUME_UP      0xE030 /* "AudioVolumeUp"      */
#define DOM_PK_AUDIO_VOLUME_UP      0xE030 /* "VolumeUp"           */
#define DOM_PK_BROWSER_HOME         0xE032 /* "BrowserHome"        */
#define DOM_PK_NUMPAD_DIVIDE        0xE035 /* "NumpadDivide"       */
#define DOM_PK_ALT_RIGHT            0xE038 /* "AltRight"           */
#define DOM_PK_HELP                 0xE03B /* "Help"               */
#define DOM_PK_NUM_LOCK             0xE045 /* "NumLock"            */
#define DOM_PK_HOME                 0xE047 /* "Home"               */
#define DOM_PK_ARROW_UP             0xE048 /* "ArrowUp"            */
#define DOM_PK_PAGE_UP              0xE049 /* "PageUp"             */
#define DOM_PK_ARROW_LEFT           0xE04B /* "ArrowLeft"          */
#define DOM_PK_ARROW_RIGHT          0xE04D /* "ArrowRight"         */
#define DOM_PK_END                  0xE04F /* "End"                */
#define DOM_PK_ARROW_DOWN           0xE050 /* "ArrowDown"          */
#define DOM_PK_PAGE_DOWN            0xE051 /* "PageDown"           */
#define DOM_PK_INSERT               0xE052 /* "Insert"             */
#define DOM_PK_DELETE               0xE053 /* "Delete"             */
#define DOM_PK_META_LEFT            0xE05B /* "MetaLeft"           */
#define DOM_PK_OS_LEFT              0xE05B /* "OSLeft"             */
#define DOM_PK_META_RIGHT           0xE05C /* "MetaRight"          */
#define DOM_PK_OS_RIGHT             0xE05C /* "OSRight"            */
#define DOM_PK_CONTEXT_MENU         0xE05D /* "ContextMenu"        */
#define DOM_PK_POWER                0xE05E /* "Power"              */
#define DOM_PK_BROWSER_SEARCH       0xE065 /* "BrowserSearch"      */
#define DOM_PK_BROWSER_FAVORITES    0xE066 /* "BrowserFavorites"   */
#define DOM_PK_BROWSER_REFRESH      0xE067 /* "BrowserRefresh"     */
#define DOM_PK_BROWSER_STOP         0xE068 /* "BrowserStop"        */
#define DOM_PK_BROWSER_FORWARD      0xE069 /* "BrowserForward"     */
#define DOM_PK_BROWSER_BACK         0xE06A /* "BrowserBack"        */
#define DOM_PK_LAUNCH_APP_1         0xE06B /* "LaunchApp1"         */
#define DOM_PK_LAUNCH_MAIL          0xE06C /* "LaunchMail"         */
#define DOM_PK_LAUNCH_MEDIA_PLAYER  0xE06D /* "LaunchMediaPlayer"  */
#define DOM_PK_MEDIA_SELECT         0xE06D /* "MediaSelect"        */

#ifdef __cplusplus
extern "C" {
#endif
/* Maps the EmscriptenKeyboardEvent::code field from emscripten/html5.h to one of the DOM_PK codes above. */
DOM_PK_CODE_TYPE emscripten_compute_dom_pk_code(const char *keyCodeString);

/* Returns the string representation of the given key code ID. Useful for debug printing. */
const char *emscripten_dom_pk_code_to_string(DOM_PK_CODE_TYPE code);
#ifdef __cplusplus
}
#endif

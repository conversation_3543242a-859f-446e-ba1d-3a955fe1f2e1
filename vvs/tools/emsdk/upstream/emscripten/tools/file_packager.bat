:: Entry point for running python scripts on windows systems.
::
:: Automatically generated by `create_entry_points.py`; DO NOT EDIT.
::
:: To make modifications to this file, edit `tools/run_python.bat` and then run
:: `tools/maint/create_entry_points.py`

:: N.b. In Windows .bat scripts, the ':' character cannot appear inside any if () blocks,
:: or there will be a parsing error.

:: All env. vars specified in this file are to be local only to this script.
@setlocal
:: -<PERSON> will not ignore _PYTHON_SYSCONFIGDATA_NAME an internal
:: of cpython used in cross compilation via setup.py.
@set _PYTHON_SYSCONFIGDATA_NAME=
@set EM_PY=%EMSDK_PYTHON%
@if "%EM_PY%"=="" (
  set EM_PY=python
)

:: Work around Windows bug https://github.com/microsoft/terminal/issues/15212 : If this
:: script is invoked via enclosing the invocation in quotes via PATH lookup, then %~f0 and
:: %~dp0 expansions will not work.
:: So first try if %~dp0 might work, and if not, manually look up this script from PATH.
@if exist "%~f0" (
  set MYDIR=%~dp0
  goto FOUND_MYDIR
)
@for %%I in (%~n0.bat) do (
  @if exist %%~$PATH:I (
    set MYDIR=%%~dp$PATH:I
  ) else (
    echo Fatal Error! Due to a Windows bug, we are unable to locate the path to %~n0.bat.
    echo To help this issue, try removing unnecessary quotes in the invocation of emcc,
    echo or add Emscripten directory to PATH.
    echo See github.com/microsoft/terminal/issues/15212 and
    echo github.com/emscripten-core/emscripten/issues/19207 for more details.
  )
)
:FOUND_MYDIR

:: Python Windows bug https://bugs.python.org/issue34780: If this script was invoked via a
:: shared stdin handle from the parent process, and that parent process stdin handle is in
:: a certain state, running python.exe might hang here. To work around this, if
:: EM_WORKAROUND_PYTHON_BUG_34780 is defined, invoke python with '< NUL' stdin to avoid
:: sharing the parent's stdin handle to it, avoiding the hang.

:: On Windows 7, the compiler batch scripts are observed to exit with a non-zero errorlevel,
:: even when the python executable above did succeed and quit with errorlevel 0 above.
:: On Windows 8 and newer, this issue has not been observed. It is possible that this
:: issue is related to the above python bug, but this has not been conclusively confirmed,
:: so using a separate env. var EM_WORKAROUND_WIN7_BAD_ERRORLEVEL_BUG to enable the known
:: workaround this issue, which is to explicitly quit the calling process with the previous
:: errorlevel from the above command.

:: Also must use goto to jump to the command dispatch, since we cannot invoke emcc from
:: inside a if() block, because if a cmdline param would contain a char '(' or ')', that
:: would throw off the parsing of the cmdline arg.
@if "%EM_WORKAROUND_PYTHON_BUG_34780%"=="" (
  @if "%EM_WORKAROUND_WIN7_BAD_ERRORLEVEL_BUG%"=="" (
    goto NORMAL
  ) else (
    goto NORMAL_EXIT
  )
) else (
  @if "%EM_WORKAROUND_WIN7_BAD_ERRORLEVEL_BUG%"=="" (
    goto MUTE_STDIN
  ) else (
    goto MUTE_STDIN_EXIT
  )
)

:NORMAL_EXIT
@"%EM_PY%" -E "%MYDIR%%~n0.py" %*
@exit %ERRORLEVEL%

:MUTE_STDIN
@"%EM_PY%" -E "%MYDIR%%~n0.py" %* < NUL
@exit /b %ERRORLEVEL%

:MUTE_STDIN_EXIT
@"%EM_PY%" -E "%MYDIR%%~n0.py" %* < NUL
@exit %ERRORLEVEL%

:NORMAL
@"%EM_PY%" -E "%MYDIR%%~n0.py" %*

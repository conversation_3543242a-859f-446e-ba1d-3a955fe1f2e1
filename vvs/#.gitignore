# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
build
.swc/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

# build outputs
dist/

# Compiled outputs
/dist
/tmp
/out-tsc
/src2

.cache
Thumbs.db
node_modules/
.build/
.vscode/extensions/**/out/
extensions/**/dist/
/out*/
/extensions/**/out/
build/node_modules
test_data/
test-results/
test-results.xml
vscode.lsif
vscode.db
/.profile-oss
/cli/target
/cli/openssl
product.overrides.json
*.snap.actual
.vscode-test

# Void added these:
.tmp/
.tmp2/
.tool-versions
src/vs/workbench/contrib/void/browser/react/out/**
src/vs/workbench/contrib/void/browser/react/src2/**

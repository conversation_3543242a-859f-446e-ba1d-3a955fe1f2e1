/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import type * as Parser from '@vscode/tree-sitter-wasm';
import { Event } from '../../../base/common/event.js';
import { ITextModel } from '../model.js';
import { createDecorator } from '../../../platform/instantiation/common/instantiation.js';
import { Range } from '../core/range.js';
import { ILogService } from '../../../platform/log/common/log.js';
import { IModelContentChangedEvent } from '../textModelEvents.js';

export const EDITOR_EXPERIMENTAL_PREFER_TREESITTER = 'editor.experimental.preferTreeSitter';
export const EDITOR_TREESITTER_TELEMETRY = 'editor.experimental.treeSitterTelemetry';
export const TREESITTER_ALLOWED_SUPPORT = [
	'css', 'typescript', 'ini', 'regex',
	'javascript', 'tsx', 'jsx', 'python', 'java', 'cpp', 'c', 'csharp',
	'go', 'rust', 'php', 'ruby', 'swift', 'kotlin', 'scala', 'html',
	'json', 'yaml', 'xml', 'markdown'
];

// Languages that are always enabled for codebase indexing (bypasses experimental settings)
export const TREESITTER_ALWAYS_ENABLED = [
	'typescript',
	'tsx'
];

export const ITreeSitterParserService = createDecorator<ITreeSitterParserService>('treeSitterParserService');

export interface RangeWithOffsets {
	range: Range;
	startOffset: number;
	endOffset: number;
}

export interface RangeChange {
	newRange: Range;
	newRangeStartOffset: number;
	newRangeEndOffset: number;
}

export interface TreeParseUpdateEvent {
	ranges: RangeChange[] | undefined;
	language: string;
	versionId: number;
	tree: Parser.Tree;
	includedModelChanges: IModelContentChangedEvent[];
}

export interface ModelTreeUpdateEvent {
	ranges: RangeChange[];
	versionId: number;
	tree: ITextModelTreeSitter;
	languageId: string;
	hasInjections: boolean;
}

export interface TreeUpdateEvent extends ModelTreeUpdateEvent {
	textModel: ITextModel;
}

export interface ITreeSitterParserService {
	readonly _serviceBrand: undefined;
	onDidAddLanguage: Event<{ id: string; language: Parser.Language }>;
	getOrInitLanguage(languageId: string): Parser.Language | undefined;
	getLanguage(languageId: string): Promise<Parser.Language | undefined>;
	getParseResult(textModel: ITextModel): ITextModelTreeSitter | undefined;
	getTree(content: string, languageId: string): Promise<Parser.Tree | undefined>;
	getTreeSync(content: string, languageId: string): Parser.Tree | undefined;
	onDidUpdateTree: Event<TreeUpdateEvent>;
	/**
	 * For testing purposes so that the time to parse can be measured.
	*/
	getTextModelTreeSitter(model: ITextModel, parseImmediately?: boolean): Promise<ITextModelTreeSitter | undefined>;
}

export interface ITreeSitterParseResult {
	readonly tree: Parser.Tree | undefined;
	readonly language: Parser.Language;
	readonly languageId: string;
	readonly ranges: Parser.Range[] | undefined;
	versionId: number;
}

export interface ITextModelTreeSitter {
	/**
	 * For testing purposes so that the time to parse can be measured.
	 */
	parse(languageId?: string): Promise<ITreeSitterParseResult | undefined>;
	textModel: ITextModel;
	parseResult: ITreeSitterParseResult | undefined;
	getInjection(offset: number, parentLanguage: string): ITreeSitterParseResult | undefined;
	dispose(): void;
}

export const ITreeSitterImporter = createDecorator<ITreeSitterImporter>('treeSitterImporter');

export interface ITreeSitterImporter {
	readonly _serviceBrand: undefined;
	getParserClass(): Promise<any>;
	readonly parserClass: any;
	getLanguageClass(): Promise<any>;
	getQueryClass(): Promise<any>;
}

export class TreeSitterImporter implements ITreeSitterImporter {
	readonly _serviceBrand: undefined;

	private _treeSitterModule: any;
	private _initPromise: Promise<void> | undefined;

	constructor(
		@ILogService private readonly _logService: ILogService
	) {
		this._logService.info('[TreeSitterImporter] Professional tree-sitter importer initialized');
	}

	private async _initializeTreeSitter(): Promise<void> {
		if (this._treeSitterModule) {
			return;
		}

		if (this._initPromise) {
			return this._initPromise;
		}

		this._initPromise = (async () => {
			try {
				this._logService.info('[TreeSitterImporter] Initializing tree-sitter module...');

				// Primary approach: Use @vscode/tree-sitter-wasm package
				try {
					const treeSitterModule = await import('@vscode/tree-sitter-wasm');
					if (treeSitterModule && treeSitterModule.Parser && treeSitterModule.Language) {
						this._treeSitterModule = treeSitterModule;
						this._logService.info('[TreeSitterImporter] Successfully loaded @vscode/tree-sitter-wasm module');
						return;
					}
				} catch (importError) {
					this._logService.warn('[TreeSitterImporter] Failed to import @vscode/tree-sitter-wasm, using fallback:', importError);
				}

				// Fallback: Create minimal implementation
				this._treeSitterModule = this._createFallbackImplementation();
				this._logService.info('[TreeSitterImporter] Using fallback tree-sitter implementation');

			} catch (error) {
				this._logService.error('[TreeSitterImporter] Failed to initialize tree-sitter:', error);
				throw error;
			}
		})();

		return this._initPromise;
	}

	private _createFallbackImplementation() {
		const logService = this._logService;

		class Parser {
			constructor() {
				logService.debug('[TreeSitterImporter] Fallback Parser instance created');
			}

			setLanguage(language: any) {
				logService.debug('[TreeSitterImporter] Fallback Parser.setLanguage called');
			}

			parse(input: string) {
				logService.debug('[TreeSitterImporter] Fallback Parser.parse called');
				return this._createSimpleTree(input);
			}

			private _createSimpleTree(input: string) {
				const lines = input.split('\n');
				return {
					rootNode: {
						type: 'program',
						startPosition: { row: 0, column: 0 },
						endPosition: { row: lines.length - 1, column: lines[lines.length - 1]?.length || 0 },
						text: input,
						childCount: 0,
						child: () => null,
						children: [],
						parent: null,
						childForFieldName: () => null
					}
				};
			}
		}

		class Language {
			static async load(wasmBytes: Uint8Array) {
				logService.info('[TreeSitterImporter] Fallback Language.load called');
				return new Language();
			}
		}

		class Query {
			constructor(language: any, queryString: string) {
				logService.debug('[TreeSitterImporter] Fallback Query created');
			}
		}

		return { Parser, Language, Query };
	}

	public async getParserClass(): Promise<any> {
		await this._initializeTreeSitter();
		return this._treeSitterModule.Parser;
	}

	public async getLanguageClass(): Promise<any> {
		await this._initializeTreeSitter();
		return this._treeSitterModule.Language;
	}

	public async getQueryClass(): Promise<any> {
		await this._initializeTreeSitter();
		return this._treeSitterModule.Query;
	}

	get parserClass(): any {
		return this._treeSitterModule?.Parser;
	}
}

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';
import { resolveAmdNodeModulePath, importAMDNodeModule } from '../../../../amdX.js';

/**
 * Language configuration for official tree-sitter packages
 */
interface LanguageConfig {
    packageName: string;
    wasmFile: string;
    languageFunction: string;
}

/**
 * Registry of official tree-sitter language packages
 */
const LANGUAGE_REGISTRY: Record<string, LanguageConfig> = {
    typescript: {
        packageName: 'tree-sitter-typescript',
        wasmFile: 'tree-sitter-typescript.wasm',
        languageFunction: 'tree_sitter_typescript'
    },
    tsx: {
        packageName: 'tree-sitter-typescript',
        wasmFile: 'tree-sitter-tsx.wasm',
        languageFunction: 'tree_sitter_tsx'
    },
    javascript: {
        packageName: 'tree-sitter-javascript',
        wasmFile: 'tree-sitter-javascript.wasm',
        languageFunction: 'tree_sitter_javascript'
    }
};

/**
 * REAL tree-sitter loader using official npm packages.
 * Loads WASM files directly from tree-sitter-typescript and other official packages.
 * NO FALLBACKS - fails honestly if WASM loading doesn't work.
 */
export class OfficialTreeSitterLoader implements ITreeSitterParserLoader {
    readonly _serviceBrand: undefined;

    private _loadedLanguages: Map<string, any> = new Map();
    private _coreTreeSitterModule: any = null;
    public parserClass?: any;

    constructor(
        @ILogService private readonly _logService: ILogService
    ) {
        this._logService.info('[OfficialTreeSitterLoader] Real WASM loader for official packages initialized');
    }

    /**
     * Load a language-specific WASM file directly from official tree-sitter packages
     */
    private async _loadLanguageWasm(languageId: string): Promise<any> {
        // Check if language is already loaded
        if (this._loadedLanguages.has(languageId)) {
            this._logService.info(`[OfficialTreeSitterLoader] Language ${languageId} already loaded from cache`);
            return this._loadedLanguages.get(languageId);
        }

        const config = LANGUAGE_REGISTRY[languageId];
        if (!config) {
            throw new Error(`No configuration found for language: ${languageId}. Available languages: ${Object.keys(LANGUAGE_REGISTRY).join(', ')}`);
        }

        try {
            this._logService.info(`[OfficialTreeSitterLoader] Loading WASM for ${languageId} from official package ${config.packageName}`);

            // Get the WASM file path from the official package
            const wasmPath = resolveAmdNodeModulePath(config.packageName, config.wasmFile);
            this._logService.info(`[OfficialTreeSitterLoader] WASM path resolved: ${wasmPath}`);

            // Load the WASM file
            const response = await fetch(wasmPath);
            if (!response.ok) {
                throw new Error(`Failed to fetch ${config.wasmFile}: ${response.status} ${response.statusText}`);
            }

            const wasmBytes = await response.arrayBuffer();
            this._logService.info(`[OfficialTreeSitterLoader] WASM loaded successfully, size: ${wasmBytes.byteLength} bytes`);

            // Ensure core tree-sitter module is loaded
            if (!this._coreTreeSitterModule) {
                await this._loadCoreTreeSitterModule();
            }

            // Use the core tree-sitter Language.load method to load the language WASM
            const language = await this._coreTreeSitterModule.Language.load(new Uint8Array(wasmBytes));
            this._loadedLanguages.set(languageId, language);
            this._logService.info(`[OfficialTreeSitterLoader] Language ${languageId} loaded successfully from official package using core tree-sitter`);

            return language;

        } catch (error) {
            this._logService.error(`[OfficialTreeSitterLoader] Failed to load language ${languageId}:`, error);
            throw error;
        }
    }

    /**
     * Load the core tree-sitter WASM module from @vscode/tree-sitter-wasm
     */
    private async _loadCoreTreeSitterModule(): Promise<any> {
        if (this._coreTreeSitterModule) {
            return this._coreTreeSitterModule;
        }

        try {
            this._logService.info('[OfficialTreeSitterLoader] Loading core tree-sitter module from @vscode/tree-sitter-wasm...');

            // Load the core tree-sitter module using VSCode's AMD system
            const treeSitterModule = await importAMDNodeModule<any>('@vscode/tree-sitter-wasm', 'wasm/tree-sitter.js');
            this._logService.info('[OfficialTreeSitterLoader] Core tree-sitter module loaded');

            // Check what type of module we got
            this._logService.info(`[OfficialTreeSitterLoader] Module type: ${typeof treeSitterModule}`);
            this._logService.info(`[OfficialTreeSitterLoader] Module keys: ${Object.keys(treeSitterModule || {}).join(', ')}`);

            // Try different ways to get the tree-sitter classes
            let treeSitterClasses;
            if (typeof treeSitterModule === 'function') {
                // UMD factory function
                treeSitterClasses = treeSitterModule();
            } else if (treeSitterModule && treeSitterModule.Parser) {
                // Direct exports
                treeSitterClasses = treeSitterModule;
            } else if (treeSitterModule && treeSitterModule.default) {
                // ES module default export
                treeSitterClasses = treeSitterModule.default;
                if (typeof treeSitterClasses === 'function') {
                    treeSitterClasses = treeSitterClasses();
                }
            } else {
                throw new Error('Unable to extract tree-sitter classes from module');
            }

            this._logService.info('[OfficialTreeSitterLoader] Tree-sitter classes extracted');
            this._logService.info(`[OfficialTreeSitterLoader] Classes available: ${Object.keys(treeSitterClasses || {}).join(', ')}`);

            // Initialize the Parser class with WASM configuration
            if (treeSitterClasses.Parser && treeSitterClasses.Parser.init) {
                await treeSitterClasses.Parser.init({
                    locateFile: (path: string) => {
                        this._logService.debug(`[OfficialTreeSitterLoader] locateFile called for: ${path}`);
                        if (path.endsWith('.wasm')) {
                            const resolvedPath = resolveAmdNodeModulePath('@vscode/tree-sitter-wasm', `wasm/${path}`);
                            this._logService.debug(`[OfficialTreeSitterLoader] Resolved WASM path: ${resolvedPath}`);
                            return resolvedPath;
                        }
                        return path;
                    }
                });
            }

            this._coreTreeSitterModule = treeSitterClasses;
            this.parserClass = treeSitterClasses;
            this._logService.info('[OfficialTreeSitterLoader] Core tree-sitter module initialized successfully');

            return treeSitterClasses;

        } catch (error) {
            this._logService.error('[OfficialTreeSitterLoader] Failed to load core tree-sitter module:', error);
            throw error;
        }
    }



    /**
     * Main entry point - load a parser for a specific language
     */
    async loadParser(_wasmUri: URI, languageId: string): Promise<any> {
        try {
            this._logService.info(`[OfficialTreeSitterLoader] Loading parser for language: ${languageId}`);

            // Handle core tree-sitter module request
            if (languageId === 'core') {
                this._logService.info('[OfficialTreeSitterLoader] Loading core tree-sitter module');
                const coreModule = await this._loadCoreTreeSitterModule();
                return coreModule;
            }

            // Ensure core module is loaded first
            if (!this._coreTreeSitterModule) {
                await this._loadCoreTreeSitterModule();
            }

            // Load the language-specific WASM from official packages
            const language = await this._loadLanguageWasm(languageId);

            // Create a parser instance using the core tree-sitter classes
            const parser = new this.parserClass.Parser();
            parser.setLanguage(language);

            this._logService.info(`[OfficialTreeSitterLoader] Parser loaded successfully for ${languageId} using official packages`);
            return parser;

        } catch (error) {
            this._logService.error(`[OfficialTreeSitterLoader] Error in loadParser for ${languageId}:`, error);
            throw error; // NO FALLBACKS - fail honestly
        }
    }
}

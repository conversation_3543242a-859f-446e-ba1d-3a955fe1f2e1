/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';
import { resolveAmdNodeModulePath, importAMDNodeModule } from '../../../../amdX.js';

/**
 * Language configuration for official tree-sitter packages
 */
interface LanguageConfig {
    packageName: string;
    wasmFiles: string[];
    languages: string[];
}

/**
 * Registry of official tree-sitter language packages
 * This enables easy extension to support additional languages
 */
const LANGUAGE_REGISTRY: Record<string, LanguageConfig> = {
    typescript: {
        packageName: 'tree-sitter-typescript',
        wasmFiles: ['tree-sitter-typescript.wasm', 'tree-sitter-tsx.wasm'],
        languages: ['typescript', 'tsx']
    },
    javascript: {
        packageName: 'tree-sitter-javascript',
        wasmFiles: ['tree-sitter-javascript.wasm'],
        languages: ['javascript']
    },
    python: {
        packageName: 'tree-sitter-python',
        wasmFiles: ['tree-sitter-python.wasm'],
        languages: ['python']
    },
    rust: {
        packageName: 'tree-sitter-rust',
        wasmFiles: ['tree-sitter-rust.wasm'],
        languages: ['rust']
    },
    go: {
        packageName: 'tree-sitter-go',
        wasmFiles: ['tree-sitter-go.wasm'],
        languages: ['go']
    },
    java: {
        packageName: 'tree-sitter-java',
        wasmFiles: ['tree-sitter-java.wasm'],
        languages: ['java']
    },
    cpp: {
        packageName: 'tree-sitter-cpp',
        wasmFiles: ['tree-sitter-cpp.wasm'],
        languages: ['cpp', 'c']
    }
};

/**
 * Professional tree-sitter loader using official npm packages.
 * Supports extensible language loading from official tree-sitter packages.
 */
export class DirectWasmTreeSitterLoader implements ITreeSitterParserLoader {
    readonly _serviceBrand: undefined;

    private _initialized = false;
    private _initPromise: Promise<void> | undefined;
    public parserClass: any;
    private _loadedLanguages: Map<string, any> = new Map();

    constructor(
        @ILogService private readonly _logService: ILogService
    ) {
        this._logService.info('[DirectWasmTreeSitterLoader] Official package-based tree-sitter loader initialized');
    }

    private async initialize(): Promise<void> {
        if (this._initialized) {
            return;
        }

        if (this._initPromise) {
            return this._initPromise;
        }

        this._initPromise = (async () => {
            try {
                this._logService.info('[DirectWasmTreeSitterLoader] Initializing with official tree-sitter packages...');

                // Load the core tree-sitter WASM module from @vscode/tree-sitter-wasm
                await this._loadCoreTreeSitterModule();
                this._logService.info('[DirectWasmTreeSitterLoader] Core tree-sitter module loaded successfully');

                this._initialized = true;

            } catch (error) {
                this._logService.error('[DirectWasmTreeSitterLoader] Failed to initialize tree-sitter loader:', error);

                // Create fallback implementation
                this.parserClass = this._createFallbackImplementation();
                this._initialized = true;
                this._logService.info('[DirectWasmTreeSitterLoader] Fallback implementation created');
            }
        })();

        return this._initPromise;
    }

    /**
     * Load the core tree-sitter WASM module from @vscode/tree-sitter-wasm
     */
    private async _loadCoreTreeSitterModule(): Promise<void> {
        try {
            this._logService.info('[DirectWasmTreeSitterLoader] Loading core tree-sitter module...');

            // Load the core tree-sitter module using VSCode's AMD system
            const treeSitterModule = await this._loadTreeSitterModule();
            this._logService.info('[DirectWasmTreeSitterLoader] Core tree-sitter module loaded');

            // The module is a UMD factory function that returns the tree-sitter classes
            const treeSitterClasses = treeSitterModule();
            this._logService.info('[DirectWasmTreeSitterLoader] Tree-sitter classes extracted');

            // Initialize the Parser class with WASM configuration
            await treeSitterClasses.Parser.init({
                locateFile: (path: string) => {
                    this._logService.debug(`[DirectWasmTreeSitterLoader] locateFile called for: ${path}`);
                    if (path.endsWith('.wasm')) {
                        const resolvedPath = resolveAmdNodeModulePath('@vscode/tree-sitter-wasm', `wasm/${path}`);
                        this._logService.debug(`[DirectWasmTreeSitterLoader] Resolved WASM path: ${resolvedPath}`);
                        return resolvedPath;
                    }
                    return path;
                }
            });

            this.parserClass = treeSitterClasses;
            this._logService.info('[DirectWasmTreeSitterLoader] Core tree-sitter module initialized successfully');

        } catch (error) {
            this._logService.error('[DirectWasmTreeSitterLoader] Failed to load core tree-sitter module:', error);
            throw error;
        }
    }

    /**
     * Load the tree-sitter JavaScript module using VSCode's AMD system
     */
    private async _loadTreeSitterModule(): Promise<any> {
        try {
            // Use importAMDNodeModule like vscode-oniguruma does
            const treeSitterModule = await importAMDNodeModule<any>('@vscode/tree-sitter-wasm', 'wasm/tree-sitter.js');
            return treeSitterModule;
        } catch (error) {
            this._logService.error('[DirectWasmTreeSitterLoader] Failed to load tree-sitter module:', error);
            throw error;
        }
    }

    /**
     * Load a language-specific WASM file from official tree-sitter packages
     */
    private async _loadLanguageWasm(languageId: string): Promise<any> {
        // Check if language is already loaded
        if (this._loadedLanguages.has(languageId)) {
            return this._loadedLanguages.get(languageId);
        }

        try {
            this._logService.info(`[DirectWasmTreeSitterLoader] Loading language WASM for: ${languageId}`);

            // Find the language configuration
            const config = this._findLanguageConfig(languageId);
            if (!config) {
                throw new Error(`No configuration found for language: ${languageId}`);
            }

            // Determine which WASM file to load
            const wasmFile = this._getWasmFileForLanguage(config, languageId);
            const wasmPath = resolveAmdNodeModulePath(config.packageName, wasmFile);

            this._logService.info(`[DirectWasmTreeSitterLoader] Loading WASM from: ${wasmPath}`);

            // Load the WASM file
            const response = await fetch(wasmPath);
            if (!response.ok) {
                throw new Error(`Failed to fetch ${wasmFile}: ${response.status} ${response.statusText}`);
            }

            const wasmBytes = new Uint8Array(await response.arrayBuffer());
            this._logService.info(`[DirectWasmTreeSitterLoader] WASM loaded, size: ${wasmBytes.byteLength} bytes`);

            // Load the language using the core tree-sitter module
            const language = await this.parserClass.Language.load(wasmBytes);
            this._loadedLanguages.set(languageId, language);

            this._logService.info(`[DirectWasmTreeSitterLoader] Language ${languageId} loaded successfully`);
            return language;

        } catch (error) {
            this._logService.error(`[DirectWasmTreeSitterLoader] Failed to load language ${languageId}:`, error);
            throw error;
        }
    }

    /**
     * Find language configuration for a given language ID
     */
    private _findLanguageConfig(languageId: string): LanguageConfig | undefined {
        for (const config of Object.values(LANGUAGE_REGISTRY)) {
            if (config.languages.includes(languageId)) {
                return config;
            }
        }
        return undefined;
    }

    /**
     * Get the appropriate WASM file for a language
     */
    private _getWasmFileForLanguage(config: LanguageConfig, languageId: string): string {
        if (languageId === 'tsx') {
            return 'tree-sitter-tsx.wasm';
        }
        if (languageId === 'typescript') {
            return 'tree-sitter-typescript.wasm';
        }
        // For other languages, use the first WASM file
        return config.wasmFiles[0];
    }





    /**
     * Creates a minimal fallback implementation when WASM loading fails.
     * This provides basic compatibility without actual tree-sitter parsing.
     */
    private _createFallbackImplementation(): any {
        const logService = this._logService;

        const Parser = class {
            constructor() {
                logService.debug('[DirectWasmTreeSitterLoader] Fallback Parser instance created');
            }

            setLanguage(language: any) {
                logService.debug('[DirectWasmTreeSitterLoader] Fallback Parser.setLanguage called');
            }

            parse(input: string) {
                logService.debug('[DirectWasmTreeSitterLoader] Fallback Parser.parse called');
                return this._createSimpleTree(input);
            }

            private _createSimpleTree(input: string) {
                const lines = input.split('\n');
                return {
                    rootNode: {
                        type: 'program',
                        startPosition: { row: 0, column: 0 },
                        endPosition: { row: lines.length - 1, column: lines[lines.length - 1]?.length || 0 },
                        text: input,
                        childCount: 0,
                        child: () => null,
                        children: [],
                        parent: null,
                        childForFieldName: () => null
                    }
                };
            }
        };

        const Language = class {
            static async load(wasmBytes: Uint8Array) {
                logService.info('[DirectWasmTreeSitterLoader] Fallback Language.load called');
                return new Language();
            }
        };

        return { Parser, Language };
    }

    async loadParser(_wasmUri: URI, languageId: string): Promise<any> {
        try {
            await this.initialize();

            if (!this.parserClass) {
                this._logService.error('[DirectWasmTreeSitterLoader] loadParser called but parserClass is not initialized');
                return undefined;
            }

            if (languageId === 'core') {
                this._logService.info('[DirectWasmTreeSitterLoader] Returning core parser module');
                return this.parserClass;
            }

            // Load language-specific parser with official packages
            this._logService.info(`[DirectWasmTreeSitterLoader] Loading parser for language: ${languageId}`);

            // Load the language-specific WASM from official packages
            const language = await this._loadLanguageWasm(languageId);

            // Create a parser instance
            const parser = new this.parserClass.Parser();
            parser.setLanguage(language);

            this._logService.info(`[DirectWasmTreeSitterLoader] Parser loaded successfully for ${languageId} using official packages`);
            return parser;

        } catch (error) {
            this._logService.error(`[DirectWasmTreeSitterLoader] Error in loadParser for ${languageId}:`, error);
            return undefined;
        }
    }
}

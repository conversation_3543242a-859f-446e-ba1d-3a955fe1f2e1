/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';

/**
 * Direct WASM tree-sitter loader that bypasses npm package dependencies
 * and loads WASM files directly. Assumes tree-sitter.wasm is the core engine
 * and language-specific WASMs are loaded separately by TreeSitterLanguages.
 */
export class DirectWasmTreeSitterLoader implements ITreeSitterParserLoader {
    readonly _serviceBrand: undefined;

    private _initialized = false;
    private _initPromise: Promise<void> | undefined;
    public parserClass: any; // This will be an object { Parser, Language }

    constructor(
        @ILogService private readonly _logService: ILogService
    ) {
        this._logService.info('[DirectWasmTreeSitterLoader] Constructor called');
    }

    private async initialize(): Promise<void> {
        if (this._initialized) {
            return;
        }

        if (this._initPromise) {
            return this._initPromise;
        }

        this._initPromise = (async () => {
            try {
                this._logService.info('[DirectWasmTreeSitterLoader] Initializing direct WASM loader...');

                // Path where the core tree-sitter.wasm is expected to be served
                // IMPORTANT: User must ensure their tree-sitter.wasm (core engine) is served at this path.
                const coreWasmUrl = '/static/resources/tree-sitter-wasm/tree-sitter.wasm';
                this._logService.info(`[DirectWasmTreeSitterLoader] Fetching core WASM from: ${coreWasmUrl}`);

                const wasmResponse = await fetch(coreWasmUrl);
                if (!wasmResponse.ok) {
                    throw new Error(`[DirectWasmTreeSitterLoader] Failed to fetch ${coreWasmUrl}: ${wasmResponse.status} ${wasmResponse.statusText}`);
                }

                const wasmBytes = await wasmResponse.arrayBuffer();

                // Define the imports object with required environment and WASI functions
                // This is crucial for Emscripten-compiled WASM like most tree-sitter.wasm files.
                const imports = {
                    env: {
                        memory: new WebAssembly.Memory({ initial: 256, maximum: 65536 }),
                        table: new WebAssembly.Table({ initial: 0, element: 'anyfunc' }),
                        abort: (message: number, fileName: number, lineNumber: number, columnNumber: number) => {
                            this._logService.error(`[DirectWasmTreeSitterLoader] WASM abort: message=${message} file=${fileName} ${lineNumber}:${columnNumber}`);
                            throw new Error(`WASM aborted at ${lineNumber}:${columnNumber}`);
                        },
                        emscripten_notify_memory_growth: (memoryIndex: number) => {
                            this._logService.info(`[DirectWasmTreeSitterLoader] WASM emscripten_notify_memory_growth: ${memoryIndex}`);
                        },
                        // Add other common Emscripten env functions if needed, e.g., for time, math
                        ___setErrNo: (value: number) => { /* no-op or log */ },
                        _gettimeofday: (ptr: number, tz: number) => { if (ptr) { new BigInt64Array(imports.env.memory.buffer, ptr, 2)[0] = BigInt(Math.floor(Date.now()/1000)); new BigInt64Array(imports.env.memory.buffer, ptr, 2)[1] = BigInt(0); } return 0; },
                        _emscripten_get_now: () => { return performance.now(); },
                    },
                    wasi_snapshot_preview1: {
                        proc_exit: (code: number) => {
                            this._logService.warn(`[DirectWasmTreeSitterLoader] WASM proc_exit with code: ${code}`);
                            if (code !== 0) { throw new Error(`WASM proc_exit with code ${code}`); }
                        },
                        fd_write: (fd: number, iovs_ptr: number, iovs_len: number, nwritten_ptr: number) => {
                            let written = 0;
                            const memoryView = new DataView(imports.env.memory.buffer);
                            let output = '';
                            for (let i = 0; i < iovs_len; i++) {
                                const buf_ptr = memoryView.getUint32(iovs_ptr + i * 8, true);
                                const buf_len = memoryView.getUint32(iovs_ptr + i * 8 + 4, true);
                                output += new TextDecoder().decode(new Uint8Array(imports.env.memory.buffer, buf_ptr, buf_len));
                                written += buf_len;
                            }
                            if (fd === 1 || fd === 2) { this._logService.info(`[DirectWasmTreeSitterLoader] WASM fd_write(fd:${fd}): ${output}`); }
                            memoryView.setUint32(nwritten_ptr, written, true);
                            return 0;
                        },
                        fd_close: (fd: number) => 0,
                        fd_seek: (fd: number, offset_low: number, offset_high: number, whence: number, newoffset_ptr: number) => 0,
                        environ_sizes_get: (environ_count_ptr: number, environ_buf_size_ptr: number) => {
                            const memoryView = new DataView(imports.env.memory.buffer);
                            memoryView.setUint32(environ_count_ptr, 0, true);
                            memoryView.setUint32(environ_buf_size_ptr, 0, true);
                            return 0;
                        },
                        environ_get: (environ_ptr: number, environ_buf_ptr: number) => 0,
                        args_sizes_get: (argc_ptr: number, argv_buf_size_ptr: number) => {
                            const memoryView = new DataView(imports.env.memory.buffer);
                            memoryView.setUint32(argc_ptr, 0, true);
                            memoryView.setUint32(argv_buf_size_ptr, 0, true);
                            return 0;
                        },
                        args_get: (argv_ptr: number, argv_buf_ptr: number) => 0,
                        random_get: (buf_ptr: number, buf_len: number) => {
                            crypto.getRandomValues(new Uint8Array(imports.env.memory.buffer, buf_ptr, buf_len));
                            return 0;
                        },
                        clock_time_get: (id: number, precision: BigInt, time_ptr: number) => {
                            if (id === 0) { // CLOCK_REALTIME
                                new BigInt64Array(imports.env.memory.buffer, time_ptr, 1)[0] = BigInt(Date.now()) * BigInt(1000000);
                                return 0;
                            }
                            return -1; // EINVAL for other clocks
                        }
                        // Other WASI stubs might be needed depending on the tree-sitter.wasm build
                    }
                };

                const instantiatedModule = await WebAssembly.instantiate(wasmBytes, imports);
                this._logService.info('[DirectWasmTreeSitterLoader] Core WASM module instantiated successfully with imports.');

                // This assumes tree-sitter.wasm (after instantiation) exports `Parser` and `Language` directly
                // or that the Emscripten glue code makes them available on `instantiatedModule.instance.exports`
                // Adjust if your specific tree-sitter.wasm has a different ABI.
                const Parser = instantiatedModule.instance.exports.Parser || instantiatedModule.instance.exports._Parser;
                const Language = instantiatedModule.instance.exports.Language || instantiatedModule.instance.exports._Language;

                if (!Parser || !Language) {
                    this._logService.error('[DirectWasmTreeSitterLoader] Parser or Language class not found on instantiated WASM module exports.', instantiatedModule.instance.exports);
                    throw new Error('Parser or Language class not found on WASM module exports.');
                }

                // The parserClass property is expected to be an object { Parser, Language }
                // by TreeSitterLanguages when it tries to load actual language grammars.
                this.parserClass = { Parser, Language };

                this._logService.info('[DirectWasmTreeSitterLoader] Direct WASM loader initialized successfully, parserClass is set.');
                this._initialized = true;

            } catch (error) {
                this._logService.error('[DirectWasmTreeSitterLoader] Failed to initialize direct WASM loader:', error);
                this.parserClass = undefined; // Ensure it's undefined on failure
                throw error;
            }
        })();

        return this._initPromise;
    }

    // This createParserClass and createLanguageClass are now effectively replaced by
    // directly assigning from the instantiated WASM module's exports.
    // Keeping them commented out for reference to original structure.
    /*
    private createParserClass(wasmModule: WebAssembly.WebAssemblyInstantiatedSource) {
        const logService = this._logService;
        return class Parser { ... };
    }

    private createLanguageClass() {
        const logService = this._logService;
        return class Language { ... };
    }
    */

    async loadParser(wasmUri: URI, languageId: string): Promise<any> {
        await this.initialize(); // Ensures parserClass is (attempted to be) set

        if (!this.parserClass) {
             this._logService.error('[DirectWasmTreeSitterLoader] loadParser called but parserClass is not initialized (core WASM loading failed).');
             return undefined;
        }

        if (languageId === 'core') {
            this._logService.info('[DirectWasmTreeSitterLoader] Returning core parser module (containing Parser and Language classes).');
            return this.parserClass; // Return the object { Parser, Language }
        }

        // For individual languages, TreeSitterLanguages will use the Language.load method from the core parser module.
        // So, we still return the core module here.
        this._logService.info(`[DirectWasmTreeSitterLoader] loadParser called for language ${languageId}, returning core parser module.`);
        return this.parserClass;
    }
}

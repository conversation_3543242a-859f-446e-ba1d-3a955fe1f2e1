/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterImporter } from '../treeSitterParserService.js';

// This file is deprecated and should not be used. All browser tree-sitter loading must use direct WASM loading, not web-tree-sitter.
export class WebTreeSitterImporter implements ITreeSitterImporter {
    readonly _serviceBrand: undefined;

    constructor(
        @ILogService private readonly _logService: ILogService
    ) {
        this._logService.info('[WebTreeSitterImporter] This importer is deprecated and should not be used.');
    }

    async getParserClass() { throw new Error('WebTreeSitterImporter is deprecated. Use direct WASM loader.'); }
    async getLanguageClass() { throw new Error('WebTreeSitterImporter is deprecated. Use direct WASM loader.'); }
    async getQueryClass() { throw new Error('WebTreeSitterImporter is deprecated. Use direct WASM loader.'); }
    get parserClass() { return undefined; }
}

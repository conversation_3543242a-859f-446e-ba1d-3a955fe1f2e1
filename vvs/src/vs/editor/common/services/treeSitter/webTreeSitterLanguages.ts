/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved. Copyright (c) 2025 Coode AI Editor
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';

export class WebTreeSitterLanguages extends Disposable {
    private readonly _onDidAddLanguage = this._register(new Emitter<{ id: string; language: any }>());
    public readonly onDidAddLanguage: Event<{ id: string; language: any }> = this._onDidAddLanguage.event;

    private readonly _languages: Map<string, any | Promise<any>> = new Map();
    private _registeredLanguages: Map<string, string> = new Map(); // languageId -> grammarName (e.g., 'typescript')
    private _loadedParsers: Map<string, any> = new Map(); // Cache for loaded language parsers

    constructor(
        @ILogService private readonly _logService: ILogService,
        @ITreeSitterParserLoader private readonly _parserLoader: ITreeSitterParserLoader,
    ) {
        super();
        this._logService.info('[WebTreeSitterLanguages] Constructor called');
    }

    public setRegisteredLanguages(registeredLanguages: Map<string, string>): void {
        this._registeredLanguages = registeredLanguages;
        this._logService.info(`[WebTreeSitterLanguages] Registered languages: ${Array.from(registeredLanguages.entries()).map(([key, value]) => `${key}: ${value}`).join(', ')}`);
    }

    public getOrInitLanguage(languageId: string): any | undefined {
        const result = this._languages.get(languageId);
        if (!result || result instanceof Promise) {
            return undefined;
        }
        return result;
    }

    public getLanguage(languageId: string): any | undefined | Promise<any> {
        if (!this._registeredLanguages.has(languageId)) {
            this._logService.warn(`[WebTreeSitterLanguages] Language ${languageId} is not registered`);
            return undefined;
        }
        return this._languages.get(languageId) ?? this._addLanguage(languageId);
    }

    private _addLanguage(languageId: string): Promise<any> {
        const grammarName = this._registeredLanguages.get(languageId);
        if (!grammarName) {
            const msg = `No grammar name registered for languageId: ${languageId}`;
            this._logService.error(msg);
            return Promise.reject(new Error(msg));
        }
        
        this._logService.info(`[WebTreeSitterLanguages] Adding language: ${languageId} with grammar: ${grammarName}`);
        
        const promise = this._fetchLanguage(languageId, grammarName);
        this._languages.set(languageId, promise);
        
        promise.then(lang => {
            this._languages.set(languageId, lang);
            this._onDidAddLanguage.fire({ id: languageId, language: lang });
            this._logService.info(`[WebTreeSitterLanguages] Language ${languageId} added successfully`);
        }).catch(err => {
            this._languages.delete(languageId);
            this._logService.error(`[WebTreeSitterLanguages] Failed to load Tree Sitter language ${languageId} (${grammarName}):`, err);
        });
        
        return promise;
    }

    private async _fetchLanguage(languageId: string, grammarName: string): Promise<any> {
        // Make sure the parser loader is initialized
        if (!this._parserLoader.parserClass) {
            this._logService.info('[WebTreeSitterLanguages] Parser class not loaded yet, loading core parser...');
            await this._parserLoader.loadParser(null as any, 'core');
        }

        const ParserWasmNamespace = this._parserLoader.parserClass;
        if (!ParserWasmNamespace) {
            this._logService.error('[WebTreeSitterLanguages] Tree Sitter Parser class not available via _parserLoader.parserClass');
            throw new Error('Tree Sitter Parser class not available via _parserLoader.parserClass.');
        }

        const { Parser, Language } = ParserWasmNamespace;
        if (!Language) {
            this._logService.error('[WebTreeSitterLanguages] Tree Sitter Language class not available from ParserWasmNamespace');
            throw new Error('Tree Sitter Language class not available from ParserWasmNamespace.');
        }

        // Construct the path relative to the application's root where WASM files are served
        let wasmFileName = '';
        if (languageId === 'typescript') {
            wasmFileName = 'tree-sitter-typescript.wasm'; // As placed by setup script
        } else if (languageId === 'tsx') {
            wasmFileName = 'tree-sitter-tsx.wasm'; // As placed by setup script
        } else {
            // For other languages, assume a consistent naming scheme: tree-sitter-GRAMMARNAME.wasm
            // The setup script would need to ensure these files are also in public/tree-sitter-wasm/
            this._logService.warn(`[WebTreeSitterLanguages] Assuming standard WASM file name for ${grammarName}: tree-sitter-${grammarName}.wasm. Ensure it's in public/tree-sitter-wasm/`);
            wasmFileName = `tree-sitter-${grammarName}.wasm`;
        }
        const wasmUrl = `/tree-sitter-wasm/${wasmFileName}`; // Path from web root

        this._logService.info(`[WebTreeSitterLanguages] Loading language WASM from URL: ${wasmUrl}`);

        try {
            const response = await fetch(wasmUrl);
            if (!response.ok) {
                const errorMsg = `Failed to fetch ${wasmUrl}: ${response.status} ${response.statusText}`;
                this._logService.error(errorMsg);
                throw new Error(errorMsg);
            }
            
            const wasmBytes = await response.arrayBuffer();
            this._logService.info(`[WebTreeSitterLanguages] WASM bytes fetched for ${languageId}, length: ${wasmBytes.byteLength}`);
            
            const lang = await Language.load(new Uint8Array(wasmBytes));
            this._logService.info(`[WebTreeSitterLanguages] Tree Sitter language ${languageId} loaded successfully`);
            
            // Create a parser with this language
            const parser = new Parser();
            parser.setLanguage(lang);
            
            // Store the language and parser
            this._loadedParsers.set(languageId, parser);
            
            return lang;
        } catch (error) {
            this._logService.error(`[WebTreeSitterLanguages] Error loading language ${languageId}:`, error);
            throw error;
        }
    }

    public getParser(languageId: string): any | undefined {
        return this._loadedParsers.get(languageId);
    }
}

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import type { Language as TreeSitterLanguageType } from '@vscode/tree-sitter-wasm';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { IEnvironmentService } from '../../../../platform/environment/common/environment.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';
import { resolveAmdNodeModulePath } from '../../../../amdX.js';

export function getModuleLocation(environmentService: IEnvironmentService): string {
	if (environmentService.isBuilt && !environmentService.isExtensionDevelopment) {
		return '../../../../../node_modules/@vscode/tree-sitter-wasm/wasm';
	}
	if (typeof window !== 'undefined') {
		return 'node_modules/@vscode/tree-sitter-wasm/wasm';
	}
	return './node_modules/@vscode/tree-sitter-wasm/wasm';
}

export class TreeSitterLanguages extends Disposable {
	private readonly _onDidAddLanguage = this._register(new Emitter<{ id: string; language: TreeSitterLanguageType }>());
	public readonly onDidAddLanguage: Event<{ id: string; language: TreeSitterLanguageType }> = this._onDidAddLanguage.event;

	private readonly _languages: Map<string, TreeSitterLanguageType | Promise<TreeSitterLanguageType>> = new Map();
	private readonly _wasmPath: string;

	private _registeredLanguages: Map<string, string> = new Map(); // languageId -> grammarName (e.g., 'tree-sitter-typescript.wasm')

	constructor(
		@IEnvironmentService private readonly _environmentService: IEnvironmentService,
		@ILogService private readonly _logService: ILogService,
		@ITreeSitterParserLoader private readonly _parserLoader: ITreeSitterParserLoader,
	) {
		super();
		this._wasmPath = getModuleLocation(this._environmentService);
		// Removed extensive debug logging for _logService from constructor
		this._logService.trace('TreeSitterLanguages wasm path:', this._wasmPath); // Keep a simple trace
	}

	public setRegisteredLanguages(registeredLanguages: Map<string, string>): void {
		this._registeredLanguages = registeredLanguages;
	}

	public getOrInitLanguage(languageId: string): TreeSitterLanguageType | undefined {
		const result = this._languages.get(languageId);
		if (!result || result instanceof Promise) {
			return undefined;
		}
		return result;
	}

	public getLanguage(languageId: string): TreeSitterLanguageType | undefined | Promise<TreeSitterLanguageType> {
		if (!this._registeredLanguages.has(languageId)) {
			return undefined;
		}
		return this._languages.get(languageId) ?? this._addLanguage(languageId);
	}

	private _addLanguage(languageId: string): Promise<TreeSitterLanguageType> {
		const grammarName = this._registeredLanguages.get(languageId);
		if (!grammarName) {
			const msg = `No grammar name registered for languageId: ${languageId}`;
			this._logService.error(msg);
			return Promise.reject(new Error(msg));
		}
		const promise = this._fetchLanguage(languageId, grammarName);
		this._languages.set(languageId, promise);
		promise.then(lang => {
			this._languages.set(languageId, lang);
			this._onDidAddLanguage.fire({ id: languageId, language: lang });
		}).catch(err => {
			this._languages.delete(languageId);
			this._logService.error(`Failed to load Tree Sitter language ${languageId} (${grammarName}):`, err);
		});
		return promise;
	}

	private async _fetchLanguage(languageId: string, grammarName: string): Promise<TreeSitterLanguageType> {
		// Ensure the parser loader is initialized
		const ParserWasmNamespace = await this._parserLoader.loadParser(undefined as any, 'core');

		if (!ParserWasmNamespace) {
			this._logService.error('[TreeSitterLanguages] Tree Sitter Parser class not available from loader');
			throw new Error('Tree Sitter Parser class not available from loader');
		}

		if (!ParserWasmNamespace.Language) {
			this._logService.error('[TreeSitterLanguages] Tree Sitter Language class not available from ParserWasmNamespace');
			throw new Error('Tree Sitter Language class not available from ParserWasmNamespace');
		}

		// Try multiple paths for WASM files with proper TypeScript/TSX handling
		const wasmPaths = this._getWasmPaths(languageId, grammarName);

		let wasmBytes: ArrayBuffer | null = null;
		let successfulPath = '';

		for (const wasmPath of wasmPaths) {
			try {
				this._logService.debug(`[TreeSitterLanguages] Trying to fetch WASM for ${languageId} from: ${wasmPath}`);
				const response = await fetch(wasmPath);
				if (response.ok) {
					wasmBytes = await response.arrayBuffer();
					successfulPath = wasmPath;
					break;
				}
			} catch (fetchError) {
				this._logService.debug(`[TreeSitterLanguages] Failed to fetch from ${wasmPath}:`, fetchError);
			}
		}

		if (!wasmBytes) {
			const errorMsg = `Failed to fetch ${grammarName}.wasm for ${languageId} from any of the attempted paths: ${wasmPaths.join(', ')}`;
			this._logService.error(errorMsg);
			throw new Error(errorMsg);
		}

		this._logService.info(`[TreeSitterLanguages] Successfully fetched WASM for ${languageId} from: ${successfulPath}, size: ${wasmBytes.byteLength} bytes`);

		try {
			const lang = await ParserWasmNamespace.Language.load(new Uint8Array(wasmBytes));
			(lang as any).languageId = languageId; // Tag for debugging
			this._logService.info(`[TreeSitterLanguages] Tree Sitter language ${languageId} (${grammarName}) loaded successfully`);
			return lang;
		} catch (loadError) {
			this._logService.error(`[TreeSitterLanguages] Error loading language ${languageId}:`, loadError);
			throw loadError;
		}
	}

	private _getWasmPaths(languageId: string, grammarName: string): string[] {
		const paths: string[] = [];

		// Primary approach: Use official tree-sitter packages first
		try {
			if (languageId === 'typescript') {
				// Prioritize tree-sitter-typescript package, fallback to @vscode/tree-sitter-wasm
				paths.push(
					resolveAmdNodeModulePath('tree-sitter-typescript', 'tree-sitter-typescript.wasm'),
					resolveAmdNodeModulePath('@vscode/tree-sitter-wasm', 'wasm/tree-sitter-typescript.wasm')
				);
			} else if (languageId === 'tsx') {
				// Prioritize tree-sitter-typescript package, fallback to @vscode/tree-sitter-wasm
				paths.push(
					resolveAmdNodeModulePath('tree-sitter-typescript', 'tree-sitter-tsx.wasm'),
					resolveAmdNodeModulePath('@vscode/tree-sitter-wasm', 'wasm/tree-sitter-tsx.wasm')
				);
			} else {
				// For other languages, use @vscode/tree-sitter-wasm
				paths.push(
					resolveAmdNodeModulePath('@vscode/tree-sitter-wasm', `wasm/${grammarName}.wasm`)
				);
			}
		} catch (resolveError) {
			this._logService.warn(`[TreeSitterLanguages] Failed to resolve AMD paths for ${languageId}:`, resolveError);
		}

		// Fallback paths for when VSCode path resolution fails
		if (languageId === 'typescript') {
			paths.push(
				'node_modules/tree-sitter-typescript/tree-sitter-typescript.wasm',
				'node_modules/@vscode/tree-sitter-wasm/wasm/tree-sitter-typescript.wasm',
				'resources/tree-sitter-wasm/tree-sitter-typescript.wasm',
				`${this._wasmPath}/tree-sitter-typescript.wasm`
			);
		} else if (languageId === 'tsx') {
			paths.push(
				'node_modules/tree-sitter-typescript/tree-sitter-tsx.wasm',
				'node_modules/@vscode/tree-sitter-wasm/wasm/tree-sitter-tsx.wasm',
				'resources/tree-sitter-wasm/tree-sitter-tsx.wasm',
				`${this._wasmPath}/tree-sitter-tsx.wasm`
			);
		} else {
			// For other languages, use standard fallback paths
			paths.push(
				`node_modules/@vscode/tree-sitter-wasm/wasm/${grammarName}.wasm`,
				`${this._wasmPath}/${grammarName}.wasm`,
				`resources/tree-sitter-wasm/${grammarName}.wasm`
			);
		}

		return paths;
	}
}

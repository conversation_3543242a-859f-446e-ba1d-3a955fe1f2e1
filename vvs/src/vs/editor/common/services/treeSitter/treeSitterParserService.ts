/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import type * as ParserWasmNamespace from '@vscode/tree-sitter-wasm';
import type { Language as TreeSitterLanguage, Tree as TreeSitterTree } from '@vscode/tree-sitter-wasm';
import { EDITOR_EXPERIMENTAL_PREFER_TREESITTER, ITreeSitterParserService, ITextModelTreeSitter, TreeUpdateEvent, ITreeSitterImporter, TREESITTER_ALLOWED_SUPPORT, TREESITTER_ALWAYS_ENABLED, ModelTreeUpdateEvent } from '../treeSitterParserService.js';
import { IModelService } from '../model.js';
import { Disposable, DisposableMap, DisposableStore } from '../../../../base/common/lifecycle.js';
import { ITextModel } from '../../model.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { IEnvironmentService } from '../../../../platform/environment/common/environment.js';
import { TextModelTreeSitter, TextModelTreeSitterItem } from './textModelTreeSitter.js';
import { getModuleLocation, TreeSitterLanguages } from './treeSitterLanguages.js';
import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';

const EDITOR_TREESITTER_TELEMETRY = 'editor.experimental.treeSitterTelemetry';
const FILENAME_TREESITTER_WASM = 'tree-sitter.wasm';

export class TreeSitterTextModelService extends Disposable implements ITreeSitterParserService {
	readonly _serviceBrand: undefined;
	private _init!: Promise<boolean>;
	private _textModelTreeSitters: DisposableMap<ITextModel, TextModelTreeSitterItem> = this._register(new DisposableMap());
	private readonly _registeredLanguages: Map<string, string> = new Map();
	private readonly _treeSitterLanguages: TreeSitterLanguages;
	private readonly _treeSitterDistUri: URI;
	private readonly _treeSitterParserLoader: ITreeSitterParserLoader;
	private readonly _logService: ILogService;
	private static _ParserModule: typeof ParserWasmNamespace | undefined;
	private static readonly FILENAME_TREESITTER_WASM = FILENAME_TREESITTER_WASM;

	public readonly onDidAddLanguage: Event<{ id: string; language: TreeSitterLanguage }>;
	private _onDidUpdateTree: Emitter<TreeUpdateEvent> = this._register(new Emitter());
	public readonly onDidUpdateTree: Event<TreeUpdateEvent> = this._onDidUpdateTree.event;

	public isTest: boolean = false;

	constructor(
		@IModelService private readonly _modelService: IModelService,
		@IFileService fileService: IFileService,
		@IConfigurationService private readonly _configurationService: IConfigurationService,
		@IEnvironmentService private readonly _environmentService: IEnvironmentService,
		@ITreeSitterImporter _treeSitterImporter: ITreeSitterImporter,
		@IInstantiationService private readonly _instantiationService: IInstantiationService,
		@ILogService logService: ILogService,
		@ITreeSitterParserLoader treeSitterParserLoader: ITreeSitterParserLoader
	) {
		super();
		this._treeSitterLanguages = this._register(this._instantiationService.createInstance(TreeSitterLanguages));
		this._treeSitterLanguages.setRegisteredLanguages(this._registeredLanguages);
		this.onDidAddLanguage = this._treeSitterLanguages.onDidAddLanguage;
		this._register(this._configurationService.onDidChangeConfiguration(e => {
			if (e.affectsConfiguration(EDITOR_EXPERIMENTAL_PREFER_TREESITTER)) {
				this._supportedLanguagesChanged();
			}
		}));
		this._supportedLanguagesChanged();
		this._treeSitterDistUri = URI.parse(getModuleLocation(this._environmentService));
		this._treeSitterParserLoader = treeSitterParserLoader;
		this._logService = logService;
	}

	getOrInitLanguage(languageId: string): TreeSitterLanguage | undefined {
		return this._treeSitterLanguages.getOrInitLanguage(languageId);
	}

	getParseResult(textModel: ITextModel): ITextModelTreeSitter | undefined {
		const textModelTreeSitter = this._textModelTreeSitters.get(textModel);
		return textModelTreeSitter?.textModelTreeSitter;
	}

	async getTree(content: string, languageId: string): Promise<TreeSitterTree | undefined> {
		const language = await this.getLanguage(languageId);
		const ParserModule = TreeSitterTextModelService._ParserModule;
		if (language && ParserModule) {
			const parser = new ParserModule.Parser();
			parser.setLanguage(language);
			return parser.parse(content) ?? undefined;
		}
		return undefined;
	}

	getTreeSync(content: string, languageId: string): TreeSitterTree | undefined {
		const language = this.getOrInitLanguage(languageId);
		const ParserModule = TreeSitterTextModelService._ParserModule;
		if (language && ParserModule) {
			const parser = new ParserModule.Parser();
			parser.setLanguage(language);
			return parser.parse(content) ?? undefined;
		}
		return undefined;
	}

	async getLanguage(languageId: string): Promise<TreeSitterLanguage | undefined> {
		await this._init;
		return this._treeSitterLanguages.getLanguage(languageId);
	}

	private _hasInit: boolean = false;
	private async _initParser(hasLanguages: boolean): Promise<boolean> {
		if (this._hasInit) {
			return this._init;
		}

		if (hasLanguages) {
			this._hasInit = true;

			// Ensure the parser loader is available
			if (!this._treeSitterParserLoader) {
				this._logService.error('[TreeSitterTextModelService] Parser loader is not available');
				this._init = Promise.resolve(false);
				return this._init;
			}

			this._init = this._treeSitterParserLoader.loadParser(
				URI.joinPath(this._treeSitterDistUri, TreeSitterTextModelService.FILENAME_TREESITTER_WASM),
				'core'
			).then(loadedCoreParserModule => {
				if (loadedCoreParserModule) {
					TreeSitterTextModelService._ParserModule = loadedCoreParserModule;
					this._logService.info('[TreeSitterTextModelService] Core tree-sitter parser module loaded successfully via loader.');
					this._registerModelServiceListeners();
					return true;
				} else {
					this._logService.error('[TreeSitterTextModelService] Failed to load core tree-sitter parser module via loader.');
					return false;
				}
			}).catch(e => {
				this._logService.error(`[TreeSitterTextModelService] Error loading core tree-sitter parser module via loader: ${e}`);
				return false;
			});
		} else {
			this._init = Promise.resolve(false);
		}
		return this._init;
	}

	private async _supportedLanguagesChanged() {
		let hasLanguages = false;

		const handleLanguage = (languageId: string) => {
			if (this._getSetting(languageId)) {
				hasLanguages = true;
				let grammarName = `tree-sitter-${languageId}`;
				if (languageId === 'typescript') {
					grammarName = 'tree-sitter-typescript';
				} else if (languageId === 'tsx') {
					grammarName = 'tree-sitter-tsx';
				} else if (languageId === 'csharp') {
					grammarName = 'tree-sitter-c-sharp';
				}

				this._addGrammar(languageId, grammarName);
			} else {
				this._removeGrammar(languageId);
			}
		};

		for (const languageId of TREESITTER_ALLOWED_SUPPORT) {
			handleLanguage(languageId);
		}

		return this._initParser(hasLanguages);
	}

	private _getSetting(languageId: string): boolean {
		// Always enable tree-sitter for languages required by codebase indexing
		if (TREESITTER_ALWAYS_ENABLED.includes(languageId)) {
			return true;
		}

		const setting = this._configurationService.getValue<boolean>(`${EDITOR_EXPERIMENTAL_PREFER_TREESITTER}.${languageId}`);
		if (!setting && TREESITTER_ALLOWED_SUPPORT.includes(languageId)) {
			return this._configurationService.getValue<boolean>(EDITOR_TREESITTER_TELEMETRY);
		}
		return !!setting;
	}

	private async _registerModelServiceListeners() {
		this._register(this._modelService.onModelAdded(model => {
			this._createTextModelTreeSitter(model);
		}));
		this._register(this._modelService.onModelRemoved(model => {
			this._textModelTreeSitters.deleteAndDispose(model);
		}));
		this._modelService.getModels().forEach(model => this._createTextModelTreeSitter(model));
	}

	public async getTextModelTreeSitter(model: ITextModel, parseImmediately: boolean = false): Promise<ITextModelTreeSitter> {
		await this.getLanguage(model.getLanguageId());
		return this._createTextModelTreeSitter(model, parseImmediately);
	}

	private _createTextModelTreeSitter(model: ITextModel, parseImmediately: boolean = true): ITextModelTreeSitter {
		const textModelTreeSitter = this._instantiationService.createInstance(TextModelTreeSitter, model, this._treeSitterLanguages, parseImmediately);
		const disposables = new DisposableStore();
		disposables.add(textModelTreeSitter);
		disposables.add(textModelTreeSitter.onDidChangeParseResult((e) => this._handleOnDidChangeParseResult(e, model)));
		this._textModelTreeSitters.set(model, {
			textModelTreeSitter,
			disposables,
			dispose: disposables.dispose.bind(disposables)
		});
		return textModelTreeSitter;
	}

	private _handleOnDidChangeParseResult(change: ModelTreeUpdateEvent, model: ITextModel) {
		this._onDidUpdateTree.fire({ textModel: model, ranges: change.ranges, versionId: change.versionId, tree: change.tree, languageId: change.languageId, hasInjections: change.hasInjections });
	}

	private _addGrammar(languageId: string, grammarName: string) {
		if (!this._registeredLanguages.has(languageId)) {
			this._registeredLanguages.set(languageId, grammarName);
		}
	}

	private _removeGrammar(languageId: string) {
		if (this._registeredLanguages.has(languageId)) {
			this._registeredLanguages.delete(languageId);
		}
	}
}




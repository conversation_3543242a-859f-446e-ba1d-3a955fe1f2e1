/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { resolveAmdNodeModulePath } from '../../../../amdX.js';

// This module provides a centralized way to access tree-sitter functionality using local WASM files

let treeSitterInitialized = false;
let treeSitterParser: any;
let treeSitterModule: any;
let languageCache: Map<string, any> = new Map();

// Load tree-sitter WASM module directly from local files
async function loadTreeSitterWasm(logService: ILogService): Promise<any> {
    if (treeSitterModule) {
        return treeSitterModule;
    }

    try {
        logService.info('[WebTreeSitter] Loading tree-sitter WASM from local files...');

        // Use VSCode's proper WASM loading mechanism
        const wasmUrl = resolveAmdNodeModulePath('tree-sitter-wasm', 'tree-sitter.wasm');
        logService.info(`[WebTreeSitter] Loading WASM from: ${wasmUrl}`);

        const wasmResponse = await fetch(wasmUrl);
        if (!wasmResponse.ok) {
            throw new Error(`Failed to fetch tree-sitter.wasm: ${wasmResponse.status} ${wasmResponse.statusText}`);
        }

        const wasmBytes = await wasmResponse.arrayBuffer();

        // Define the imports object with required environment and WASI functions
        const imports = {
            env: {
                memory: new WebAssembly.Memory({ initial: 256, maximum: 65536 }), // 16MB initial, 4GB max
                table: new WebAssembly.Table({ initial: 0, element: 'anyfunc' }),
                abort: (message: number, fileName: number, lineNumber: number, columnNumber: number) => {
                    logService.error(`[WebTreeSitter] WASM abort: message=${message} file=${fileName} ${lineNumber}:${columnNumber}`);
                    throw new Error(`WASM aborted at ${lineNumber}:${columnNumber}`);
                },
                log: (msg: number) => {
                    logService.info(`[WebTreeSitter] WASM log: ${msg}`);
                },
                emscripten_notify_memory_growth: (memoryIndex: number) => {
                    logService.info(`[WebTreeSitter] WASM emscripten_notify_memory_growth: ${memoryIndex}`);
                },
                // Other potential env imports if needed by tree-sitter's specific build
            },
            wasi_snapshot_preview1: {
                proc_exit: (code: number) => {
                    logService.info(`[WebTreeSitter] WASM proc_exit with code: ${code}`);
                    if (code !== 0) {
                        // Potentially throw an error or handle non-zero exit codes
                        // For now, just log, as throwing might stop all parsing.
                        logService.warn(`[WebTreeSitter] WASM proc_exit with non-zero code: ${code}`);
                    }
                },
                fd_write: (fd: number, iovs_ptr: number, iovs_len: number, nwritten_ptr: number) => {
                    // Minimal implementation for fd_write, often used for console output
                    // This assumes that the tree-sitter WASM might try to log to stdout/stderr
                    let written = 0;
                    const memory = imports.env.memory; // Get the memory instance
                    const buffer = new Uint8Array(memory.buffer);
                    const iovs = new Uint32Array(memory.buffer, iovs_ptr, iovs_len * 2);

                    let output = '';
                    for (let i = 0; i < iovs_len; i++) {
                        const buf_ptr = iovs[i * 2];
                        const buf_len = iovs[i * 2 + 1];
                        output += new TextDecoder().decode(buffer.subarray(buf_ptr, buf_ptr + buf_len));
                        written += buf_len;
                    }
                    if (fd === 1 || fd === 2) { // stdout or stderr
                        logService.info(`[WebTreeSitter] WASM fd_write(fd:${fd}): ${output}`);
                    }
                    new Uint32Array(memory.buffer, nwritten_ptr, 1)[0] = written;
                    return 0; // 0 on success
                },
                fd_close: (fd: number) => {
                    logService.info(`[WebTreeSitter] WASM fd_close: ${fd}`);
                    return 0; // 0 on success
                },
                fd_seek: (fd: number, offset_low: number, offset_high: number, whence: number, newoffset_ptr: number) => {
                    logService.info(`[WebTreeSitter] WASM fd_seek: fd=${fd} offset_low=${offset_low} whence=${whence}`);
                    // Typically not needed for basic tree-sitter parsing in this environment
                    return 0; // Indicate success, actual seeking not implemented
                },
                environ_sizes_get: (environ_count_ptr: number, environ_buf_size_ptr: number) => {
                    const memory = imports.env.memory;
                    new Uint32Array(memory.buffer, environ_count_ptr, 1)[0] = 0;
                    new Uint32Array(memory.buffer, environ_buf_size_ptr, 1)[0] = 0;
                    return 0;
                },
                environ_get: (environ_ptr: number, environ_buf_ptr: number) => {
                    return 0;
                },
                args_sizes_get: (argc_ptr: number, argv_buf_size_ptr: number) => {
                    const memory = imports.env.memory;
                    new Uint32Array(memory.buffer, argc_ptr, 1)[0] = 0;
                    new Uint32Array(memory.buffer, argv_buf_size_ptr, 1)[0] = 0;
                    return 0;
                },
                args_get: (argv_ptr: number, argv_buf_ptr: number) => {
                    return 0;
                },
                // Add other stubs as required by tree-sitter
                random_get: (buf_ptr: number, buf_len: number) => {
                    const memory = imports.env.memory;
                    const buffer = new Uint8Array(memory.buffer, buf_ptr, buf_len);
                    crypto.getRandomValues(buffer);
                    return 0;
                },
                clock_time_get: (id: number, precision: BigInt, time_ptr: number) => {
                    const memory = imports.env.memory;
                    if (id === 0) { // CLOCK_REALTIME
                        const now = BigInt(Date.now()) * BigInt(1000000); // nanoseconds
                        new BigUint64Array(memory.buffer, time_ptr, 1)[0] = now;
                        return 0;
                    }
                    return -1; // EINVAL for other clocks
                }
            }
        };

        // Instantiate the WASM module with imports
        const wasmModule = await WebAssembly.instantiate(wasmBytes, imports);
        logService.info('[WebTreeSitter] WASM module instantiated successfully');

        // Create a minimal tree-sitter module interface
        class Parser {
            private _language: any;
            private wasmInstance: any;

            constructor() {
                this.wasmInstance = wasmModule.instance;
            }

            setLanguage(language: any) {
                this._language = language;
                // Call the appropriate WASM export to set the language
                if (this.wasmInstance.exports.ts_parser_set_language) {
                    this.wasmInstance.exports.ts_parser_set_language(this._language);
                }
            }

            parse(input: string) {
                logService.info('[WebTreeSitter] Parsing with WASM implementation');
                try {
                    // Call the appropriate WASM export to parse
                    if (this.wasmInstance.exports.ts_parser_parse_string) {
                        const result = this.wasmInstance.exports.ts_parser_parse_string(null, input, input.length);
                        return this.createTreeFromWasm(result);
                    }
                    return this.createSimpleTree(input);
                } catch (error) {
                    logService.error('[WebTreeSitter] Error during WASM parse:', error);
                    return this.createSimpleTree(input);
                }
            }

            private createTreeFromWasm(wasmResult: any) {
                // Convert WASM result to tree structure
                // This is a placeholder - actual implementation would depend on WASM module structure
                return {
                    rootNode: {
                        type: 'program',
                        startPosition: { row: 0, column: 0 },
                        endPosition: { row: 0, column: 0 },
                        text: '',
                        childCount: 0,
                        child: () => null,
                        children: []
                    }
                };
            }

            private createSimpleTree(input: string) {
                return {
                    rootNode: {
                        type: 'program',
                        startPosition: { row: 0, column: 0 },
                        endPosition: { row: input.split('\n').length - 1, column: 0 },
                        text: input,
                        childCount: 0,
                        child: () => null,
                        children: []
                    }
                };
            }
        }

        // Create Language class
        class Language {
            static async load(wasmBytes: Uint8Array) {
                logService.info('[WebTreeSitter] Loading language from WASM bytes');
                try {
                    // Instantiate the language WASM with the same imports
                    const langModule = await WebAssembly.instantiate(wasmBytes, imports);
                    return langModule.instance;
                } catch (error) {
                    logService.error('[WebTreeSitter] Error loading language WASM:', error);
                    return new Language();
                }
            }
        }

        treeSitterModule = {
            Parser,
            Language
        };

        logService.info('[WebTreeSitter] Successfully loaded tree-sitter WASM module');
        return treeSitterModule;

    } catch (error) {
        logService.error('[WebTreeSitter] Error loading tree-sitter WASM:', error);
        throw error;
    }
}

// Initialize tree-sitter and return a parser instance
export async function getWebTreeSitterParser(logService: ILogService): Promise<any> {
    if (treeSitterParser) {
        return treeSitterParser;
    }

    try {
        const module = await loadTreeSitterWasm(logService);
        treeSitterParser = new module.Parser();
        treeSitterInitialized = true;
        logService.info('[WebTreeSitter] Successfully initialized tree-sitter parser');

        return treeSitterParser;
    } catch (error) {
        logService.error('[WebTreeSitter] Error initializing tree-sitter parser:', error);
        throw error;
    }
}

// Load a language by its ID
export async function getWebTreeSitterLanguage(languageId: string, logService: ILogService): Promise<any> {
    // Check cache first
    if (languageCache.has(languageId)) {
        return languageCache.get(languageId);
    }

    if (!treeSitterInitialized) {
        await getWebTreeSitterParser(logService);
    }

    // Get the tree-sitter module
    const module = await loadTreeSitterWasm(logService);

    try {
        // Determine WASM filename based on language ID
        let wasmFilename: string;

        if (languageId === 'typescript') {
            wasmFilename = 'tree-sitter-typescript.wasm';
        } else if (languageId === 'tsx') {
            wasmFilename = 'tree-sitter-tsx.wasm';
        } else {
            wasmFilename = `tree-sitter-${languageId}.wasm`;
        }

        // Use VSCode's proper WASM loading mechanism for language files
        const languageWasmUrl = resolveAmdNodeModulePath('tree-sitter-wasm', wasmFilename);
        logService.info(`[WebTreeSitter] Loading language WASM from: ${languageWasmUrl}`);

        const response = await fetch(languageWasmUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch ${wasmFilename}: ${response.status} ${response.statusText}`);
        }

        const bytes = await response.arrayBuffer();
        const language = await module.Language.load(new Uint8Array(bytes));

        // Cache the language
        languageCache.set(languageId, language);

        logService.info(`[WebTreeSitter] Successfully loaded language: ${languageId}`);
        return language;
    } catch (error) {
        logService.error(`[WebTreeSitter] Error loading language ${languageId}:`, error);
        throw error;
    }
}

// Parse content with tree-sitter
export async function parseWithWebTreeSitter(content: string, languageId: string, logService: ILogService): Promise<any> {
    try {
        const parser = await getWebTreeSitterParser(logService);
        const language = await getWebTreeSitterLanguage(languageId, logService);

        parser.setLanguage(language);
        const tree = parser.parse(content);

        return tree;
    } catch (error) {
        logService.error(`[WebTreeSitter] Error parsing content with language ${languageId}:`, error);
        throw error;
    }
}

// Helper class for managing tree-sitter resources
export class WebTreeSitterManager extends Disposable {
    private static _instance: WebTreeSitterManager;

    private constructor(private readonly logService: ILogService) {
        super();
    }

    public static getInstance(logService: ILogService): WebTreeSitterManager {
        if (!WebTreeSitterManager._instance) {
            WebTreeSitterManager._instance = new WebTreeSitterManager(logService);
        }
        return WebTreeSitterManager._instance;
    }

    public async getParser(): Promise<any> {
        return getWebTreeSitterParser(this.logService);
    }

    public async getLanguage(languageId: string): Promise<any> {
        return getWebTreeSitterLanguage(languageId, this.logService);
    }

    public async parse(content: string, languageId: string): Promise<any> {
        return parseWithWebTreeSitter(content, languageId, this.logService);
    }

    public override dispose(): void {
        super.dispose();
        if (treeSitterParser) {
            treeSitterParser = undefined;
        }
        languageCache.clear();
        treeSitterInitialized = false;
        treeSitterModule = undefined;
    }
}
